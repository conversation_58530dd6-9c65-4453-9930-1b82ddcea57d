const express = require('express');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const logger = require('../core/logger');

const router = express.Router();

/**
 * Listar contas com filtros e paginação
 * GET /api/accounts
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      search = '',
      account_type = '',
      status = '',
      branch_id = '',
      client_id = '',
      start_date = '',
      end_date = '',
      page = 1,
      limit = 20
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Construir query base
    let whereConditions = ['1=1'];
    let queryParams = [];

    // Filtros
    if (search) {
      whereConditions.push(`(a.account_number LIKE ? OR c.full_name LIKE ? OR c.company_name LIKE ?)`);
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    if (account_type) {
      whereConditions.push(`a.account_type = ?`);
      queryParams.push(account_type);
    }

    if (status) {
      whereConditions.push(`a.status = ?`);
      queryParams.push(status);
    }

    if (branch_id) {
      whereConditions.push(`a.branch_id = ?`);
      queryParams.push(parseInt(branch_id));
    }

    if (client_id) {
      whereConditions.push(`ah.client_id = ?`);
      queryParams.push(client_id);
    }

    if (start_date) {
      whereConditions.push(`DATE(a.opening_date) >= ?`);
      queryParams.push(start_date);
    }

    if (end_date) {
      whereConditions.push(`DATE(a.opening_date) <= ?`);
      queryParams.push(end_date);
    }

    const whereClause = whereConditions.join(' AND ');

    // Query para contar total de registros
    const countQuery = `
      SELECT COUNT(DISTINCT a.id) as total
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      WHERE ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / parseInt(limit));

    // Query principal para buscar contas
    const accountsQuery = `
      SELECT
        a.*,
        b.name as branch_name,
        c.full_name as primary_holder_name,
        c.company_name as primary_holder_company,
        c.client_type as primary_holder_type,
        curr.code as currency_code,
        curr.symbol as currency_symbol
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      LEFT JOIN currencies curr ON a.currency_id = curr.id
      WHERE ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const accounts = await executeQuery(accountsQuery, [...queryParams, parseInt(limit), offset]);

    // Para cada conta, buscar todos os titulares
    for (let account of accounts) {
      const holdersQuery = `
        SELECT
          ah.*,
          c.full_name,
          c.company_name,
          c.client_type,
          c.document_type,
          c.document_number
        FROM account_holders ah
        JOIN clients c ON ah.client_id = c.id
        WHERE ah.account_id = ?
        ORDER BY ah.holder_type, ah.created_at
      `;

      account.holders = await executeQuery(holdersQuery, [account.id]);
    }

    res.status(200).json({
      status: 'success',
      data: {
        accounts,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_records: totalRecords,
          records_per_page: parseInt(limit),
          has_next: parseInt(page) < totalPages,
          has_previous: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Erro ao listar contas:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: 'Não foi possível listar as contas'
    });
  }
});

// ENDPOINT DE CRIAÇÃO DIRETA DE CONTAS REMOVIDO
// Conforme solicitado na Tarefa 7: Remoção da Funcionalidade 'Criar Conta Direta'
// Manter apenas o fluxo: Abertura de Conta → Aprovação de Contas → Criação Automática

/**
 * Atualizar conta bancária
 * PUT /api/accounts/:id
 */
router.put('/:id', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Verificar se a conta existe
    const existingAccount = await executeQuery(
      'SELECT id, account_number, status FROM accounts WHERE id = ?',
      [id]
    );

    if (!existingAccount || existingAccount.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Conta não encontrada'
      });
    }

    // Construir query de atualização
    const updateFields = [];
    const updateValues = [];

    // Campos permitidos para atualização
    const allowedFields = ['status', 'overdraft_limit', 'account_type'];

    Object.keys(updates).forEach(key => {
      if (allowedFields.includes(key) && updates[key] !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(updates[key]);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Nenhum campo válido para atualizar'
      });
    }

    // Adicionar timestamp de atualização
    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    // Executar atualização
    const updateQuery = `
      UPDATE accounts
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await executeQuery(updateQuery, updateValues);

    // Buscar conta atualizada com todos os dados
    const updatedAccountQuery = `
      SELECT
        a.*,
        b.name as branch_name,
        c.full_name as primary_holder_name,
        c.company_name as primary_holder_company,
        c.client_type as primary_holder_type,
        curr.code as currency_code,
        curr.symbol as currency_symbol
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      LEFT JOIN currencies curr ON a.currency_id = curr.id
      WHERE a.id = ?
    `;

    const updatedAccount = await executeQuery(updatedAccountQuery, [id]);

    logger.info(`Conta atualizada: ${existingAccount[0].account_number}`, {
      accountId: id,
      updatedBy: req.user.id,
      fields: Object.keys(updates)
    });

    res.status(200).json({
      status: 'success',
      message: 'Conta atualizada com sucesso',
      data: {
        account: updatedAccount[0]
      }
    });

  } catch (error) {
    logger.error('Erro ao atualizar conta:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: 'Não foi possível atualizar a conta'
    });
  }
});

/**
 * Adicionar segundo titular a uma conta existente
 * POST /api/accounts/:id/add-holder
 */
router.post('/:id/add-holder', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const { id: accountId } = req.params;
    const { client_id } = req.body;

    // Validações básicas
    if (!client_id) {
      return res.status(400).json({
        status: 'error',
        message: 'ID do cliente é obrigatório'
      });
    }

    // Verificar se a conta existe e é do tipo 'corrente'
    const accountQuery = `
      SELECT id, account_type, status
      FROM accounts
      WHERE id = ?
    `;
    const account = await executeQuery(accountQuery, [accountId]);

    if (!account || account.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Conta não encontrada'
      });
    }

    if (account[0].account_type !== 'corrente') {
      return res.status(400).json({
        status: 'error',
        message: 'Apenas contas do tipo "Conta Particular/Singular" podem ter segundo titular'
      });
    }

    // Verificar se o cliente existe
    const clientQuery = `
      SELECT id, full_name, document_number
      FROM clients
      WHERE id = ?
    `;
    const client = await executeQuery(clientQuery, [client_id]);

    if (!client || client.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Cliente não encontrado'
      });
    }

    // Verificar se o cliente já é titular desta conta
    const existingHolderQuery = `
      SELECT id
      FROM account_holders
      WHERE account_id = ? AND client_id = ?
    `;
    const existingHolder = await executeQuery(existingHolderQuery, [accountId, client_id]);

    if (existingHolder && existingHolder.length > 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Este cliente já é titular desta conta'
      });
    }

    // Verificar quantos titulares a conta já tem
    const holdersCountQuery = `
      SELECT COUNT(*) as count
      FROM account_holders
      WHERE account_id = ?
    `;
    const holdersCount = await executeQuery(holdersCountQuery, [accountId]);

    if (holdersCount[0].count >= 2) {
      return res.status(400).json({
        status: 'error',
        message: 'Esta conta já possui o número máximo de titulares (2)'
      });
    }

    // Adicionar segundo titular
    const currentDate = new Date();
    const holderId = `holder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const addHolderQuery = `
      INSERT INTO account_holders (id, account_id, client_id, holder_type, created_at)
      VALUES (?, ?, ?, 'secondary', ?)
    `;

    await executeQuery(addHolderQuery, [
      holderId,
      accountId,
      client_id,
      currentDate
    ]);

    // Log da operação
    logger.info(`Segundo titular adicionado à conta ${accountId}`, {
      accountId,
      clientId: client_id,
      clientName: client[0].full_name,
      userId: req.user.id,
      timestamp: currentDate
    });

    res.status(200).json({
      status: 'success',
      message: 'Segundo titular adicionado com sucesso',
      data: {
        holderId,
        accountId,
        clientId: client_id,
        clientName: client[0].full_name,
        holderType: 'secondary'
      }
    });

  } catch (error) {
    logger.error('Erro ao adicionar segundo titular:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: 'Não foi possível adicionar o segundo titular'
    });
  }
});

module.exports = router;
