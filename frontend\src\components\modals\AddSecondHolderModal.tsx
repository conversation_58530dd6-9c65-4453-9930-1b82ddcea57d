import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertCircle, UserPlus, Search } from 'lucide-react';
import { clientService } from '@/services/clientService';
import { accountService } from '@/services/accountService';
import { Client } from '@/types/client';
import { Account } from '@/services/accountService';

interface AddSecondHolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  account: Account | null;
  onHolderAdded?: () => void;
}

const AddSecondHolderModal: React.FC<AddSecondHolderModalProps> = ({
  isOpen,
  onClose,
  account,
  onHolderAdded,
}) => {
  const { toast } = useToast();

  // Estados do formulário
  const [clientSearch, setClientSearch] = useState('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [searchResults, setSearchResults] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Buscar clientes
  const searchClients = async (searchTerm: string) => {
    if (searchTerm.length < 3) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await clientService.getClients({
        search: searchTerm,
        limit: 10,
        client_type: 'individual' // Apenas clientes individuais podem ser segundo titular
      });
      setSearchResults(response.clients);
    } catch (error: any) {
      console.error('Erro ao buscar clientes:', error);
      toast({
        title: "Erro ao buscar clientes",
        description: error.message || "Erro interno do servidor",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Effect para busca automática
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (clientSearch) {
        searchClients(clientSearch);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [clientSearch]);

  // Selecionar cliente
  const handleSelectClient = (client: Client) => {
    setSelectedClient(client);
    setClientSearch(client.full_name || '');
    setSearchResults([]);
    setErrors({});
  };

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!selectedClient) {
      newErrors.client = 'Selecione um cliente para ser segundo titular';
    }

    if (!account) {
      newErrors.account = 'Conta não encontrada';
    }

    // Verificar se é conta corrente (particular)
    if (account && account.account_type !== 'corrente') {
      newErrors.account_type = 'Apenas contas particulares podem ter segundo titular';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Chamar endpoint para adicionar segundo titular
      await accountService.addSecondHolder(account!.id, selectedClient!.id);

      toast({
        title: "Segundo titular adicionado",
        description: `${selectedClient!.full_name} foi adicionado como segundo titular da conta ${account!.account_number}`,
      });

      // Resetar formulário
      setSelectedClient(null);
      setClientSearch('');
      setErrors({});

      // Chamar callback e fechar modal
      onHolderAdded?.();
      onClose();
    } catch (error: any) {
      console.error('Erro ao adicionar segundo titular:', error);
      toast({
        title: "Erro ao adicionar segundo titular",
        description: error.message || "Erro interno do servidor",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Resetar formulário ao fechar
  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedClient(null);
      setClientSearch('');
      setSearchResults([]);
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-twins-primary" />
            Adicionar Segundo Titular
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informações da Conta */}
          {account && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium">Conta: {account.account_number}</p>
              <p className="text-sm text-gray-600">Tipo: {account.account_type}</p>
            </div>
          )}

          {/* Busca de Cliente */}
          <div className="space-y-2">
            <Label htmlFor="client-search">
              Buscar Cliente <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="client-search"
                placeholder="Digite nome ou documento do cliente..."
                value={clientSearch}
                onChange={(e) => setClientSearch(e.target.value)}
                className={`pl-10 ${errors.client ? 'border-red-500' : ''}`}
              />
              {isSearching && (
                <Loader2 className="absolute right-3 top-3 h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>
            {errors.client && (
              <p className="text-sm text-red-500">{errors.client}</p>
            )}
          </div>

          {/* Resultados da Busca */}
          {searchResults.length > 0 && (
            <div className="border rounded-lg max-h-40 overflow-y-auto">
              {searchResults.map((client) => (
                <div
                  key={client.id}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                  onClick={() => handleSelectClient(client)}
                >
                  <p className="font-medium">{client.full_name}</p>
                  <p className="text-sm text-gray-600">{client.document_number}</p>
                </div>
              ))}
            </div>
          )}

          {/* Cliente Selecionado */}
          {selectedClient && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="font-medium text-green-800">Cliente Selecionado:</p>
              <p className="text-green-700">{selectedClient.full_name}</p>
              <p className="text-sm text-green-600">{selectedClient.document_number}</p>
            </div>
          )}

          {/* Alertas */}
          {errors.account_type && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.account_type}</AlertDescription>
            </Alert>
          )}

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              O segundo titular terá os mesmos direitos sobre a conta que o titular principal.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting || !selectedClient}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Adicionar Titular
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddSecondHolderModal;
