# Limite de Descoberto - Sistema K-Bank

## 📋 **Conceito e Definição**

O **Limite de Descoberto** é uma facilidade bancária que permite ao cliente movimentar valores superiores ao saldo disponível em sua conta corrente, até um limite pré-aprovado pelo banco.

### **Características Principais:**
- **Crédito Pré-Aprovado**: Valor disponível imediatamente sem necessidade de nova análise
- **Rotativo**: Pode ser utilizado e quitado múltiplas vezes
- **Juros Diários**: Cobrança apenas sobre o valor utilizado
- **Flexibilidade**: Ideal para necessidades de caixa temporárias

---

## 🏦 **Como Funciona no Sistema K-Bank**

### **1. Configuração da Conta**
```
Saldo Atual: 10.000,00 Kz
Limite de Descoberto: 500.000,00 Kz
Saldo Disponível Total: 510.000,00 Kz
```

### **2. Cenários de Uso**

#### **Cenário A: Saque Dentro do Saldo**
- **Operação**: Saque de 8.000,00 Kz
- **Resultado**: Saldo = 2.000,00 Kz (sem usar descoberto)
- **Juros**: Não há cobrança

#### **Cenário B: Saque Usando Descoberto**
- **Operação**: Saque de 15.000,00 Kz
- **Resultado**: Saldo = -5.000,00 Kz (descoberto utilizado)
- **Juros**: Cobrança sobre 5.000,00 Kz utilizados

#### **Cenário C: Limite Excedido**
- **Operação**: Tentativa de saque de 520.000,00 Kz
- **Resultado**: Operação NEGADA (excede limite total)
- **Sistema**: Exibe mensagem de saldo insuficiente

---

## 💻 **Implementação Técnica**

### **Campos na Base de Dados**
```sql
-- Tabela: accounts
overdraft_limit DECIMAL(15,2) DEFAULT 0.00 -- Limite de descoberto em Kz
```

### **Cálculo do Saldo Disponível**
```javascript
const saldoDisponivel = saldoAtual + limiteDesco berto;

// Exemplo:
// Saldo Atual: 10.000,00 Kz
// Limite Descoberto: 500.000,00 Kz
// Saldo Disponível: 510.000,00 Kz
```

### **Validação de Operações**
```javascript
function validarOperacao(valorOperacao, saldoAtual, limiteDescoberto) {
  const saldoDisponivel = saldoAtual + limiteDescoberto;
  
  if (valorOperacao <= saldoDisponivel) {
    return { permitido: true, usaDescoberto: valorOperacao > saldoAtual };
  } else {
    return { permitido: false, motivo: "Saldo insuficiente" };
  }
}
```

---

## 🎯 **Tipos de Conta e Limites**

### **Conta Particular/Singular**
- **Limite Padrão**: 0,00 Kz (sem descoberto)
- **Limite Máximo**: 1.000.000,00 Kz (após análise)
- **Critérios**: Renda comprovada, histórico bancário

### **Conta Salário**
- **Limite Padrão**: 50.000,00 Kz
- **Limite Máximo**: 500.000,00 Kz
- **Critérios**: Baseado no salário mensal

### **Conta Júnior (2 titular)**
- **Limite Padrão**: 0,00 Kz (sem descoberto)
- **Limite Máximo**: 200.000,00 Kz
- **Critérios**: Aprovação de ambos os titulares

---

## ⚠️ **Regras de Negócio**

### **Aprovação de Limites**
1. **Análise de Crédito**: Renda, histórico, garantias
2. **Aprovação Hierárquica**: Gerente → Diretor (conforme valor)
3. **Revisão Periódica**: Reavaliação a cada 12 meses

### **Cobrança de Juros**
- **Taxa**: Definida pela política do banco
- **Cálculo**: Diário sobre saldo devedor
- **Cobrança**: Automática no final do mês

### **Bloqueios e Restrições**
- **Conta Bloqueada**: Descoberto suspenso
- **Inadimplência**: Limite reduzido ou cancelado
- **Conta Inativa**: Descoberto não disponível

---

## 🔧 **Interface do Sistema**

### **Visualização para o Cliente**
```
┌─────────────────────────────────────┐
│ Detalhes da Conta                   │
├─────────────────────────────────────┤
│ Saldo Atual:        10.000,00 Kz   │
│ Saldo Disponível:  510.000,00 Kz   │
│ Limite Descoberto: 500.000,00 Kz   │
└─────────────────────────────────────┘
```

### **Configuração pelo Banco**
- **Campo Editável**: Limite de Descoberto
- **Validação**: Valores positivos apenas
- **Histórico**: Log de alterações de limite
- **Aprovação**: Workflow de aprovação

---

## 📊 **Relatórios e Controles**

### **Relatórios Gerenciais**
- **Utilização de Descoberto**: Por cliente, período
- **Receita de Juros**: Projeções e realizações
- **Inadimplência**: Clientes em descoberto prolongado

### **Alertas do Sistema**
- **Limite Próximo**: 90% do limite utilizado
- **Descoberto Prolongado**: Mais de 30 dias negativos
- **Revisão de Limite**: Vencimento da análise

---

## 🚀 **Benefícios para o Banco**

1. **Receita Adicional**: Juros sobre descoberto utilizado
2. **Fidelização**: Conveniência para o cliente
3. **Competitividade**: Diferencial no mercado
4. **Gestão de Risco**: Limites controlados e monitorados

---

## ✅ **Conclusão**

O Limite de Descoberto é uma ferramenta essencial no sistema bancário moderno, proporcionando flexibilidade financeira aos clientes e receita adicional ao banco. No Sistema K-Bank, está implementado de forma robusta com controles adequados de risco e interface intuitiva.

**Valor Atual Configurado**: 500.000,00 Kz (conforme screenshot fornecido)
