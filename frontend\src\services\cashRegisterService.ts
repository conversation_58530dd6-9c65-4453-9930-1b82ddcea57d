import { tokenManager } from '@/utils/tokenManager';

// Interfaces para operações de caixa
export interface CashRegisterSession {
  id: string;
  user_id: string;
  branch_id: number;
  cash_register_number: string;
  opening_balance: number;
  current_balance: number;
  status: 'open' | 'closed';
  opened_at: string;
  closed_at?: string;
  denominations?: CashDenominations;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CashDenominations {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
  notes_100: number;
  notes_50: number;
  coins_10: number;
  coins_5: number;
  coins_1: number;
}

export interface OpenCashRegisterRequest {
  cash_register_number: string;
  opening_balance: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface CloseCashRegisterRequest {
  closing_balance: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface CashTransaction {
  id: string;
  session_id: string;
  account_id?: string;
  transaction_type: 'deposit' | 'withdrawal' | 'transfer_in' | 'transfer_out';
  amount: number;
  description: string;
  reference_number?: string;
  client_name?: string;
  account_number?: string;
  created_at: string;
  created_by: string;
}

export interface CashTransactionRequest {
  account_id: string;
  transaction_type: 'deposit' | 'withdrawal';
  amount: number;
  description: string;
  reference_number?: string;
}

export interface CashRegisterListResponse {
  sessions: CashRegisterSession[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    records_per_page: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

export interface CashRegisterFilters {
  status?: 'open' | 'closed';
  user_id?: string;
  branch_id?: number;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

// Função auxiliar para fazer requisições com retry
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<{ data?: T; status: string; message?: string }> => {
  const token = tokenManager.getAccessToken();
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(`http://localhost:3001${endpoint}`, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error(`Tentativa ${attempt} falhou:`, error);
      
      if (attempt === retries) {
        throw error;
      }
      
      // Aguardar antes da próxima tentativa
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw new Error('Falha após múltiplas tentativas');
};

class CashRegisterService {
  // Abrir caixa
  async openCashRegister(data: OpenCashRegisterRequest): Promise<CashRegisterSession> {
    const response = await makeRequest<CashRegisterSession>('/api/cash-register/open', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao abrir caixa');
  }

  // Fechar caixa
  async closeCashRegister(sessionId: string, data: CloseCashRegisterRequest): Promise<CashRegisterSession> {
    const response = await makeRequest<CashRegisterSession>(`/api/cash-register/${sessionId}/close`, {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao fechar caixa');
  }

  // Listar sessões de caixa
  async getCashRegisterSessions(filters: CashRegisterFilters = {}): Promise<CashRegisterListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<CashRegisterListResponse>(
      `/api/cash-register/sessions?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar sessões de caixa');
  }

  // Obter sessão atual do usuário
  async getCurrentSession(): Promise<CashRegisterSession | null> {
    try {
      const response = await makeRequest<CashRegisterSession>('/api/cash-register/current');
      return response.data || null;
    } catch (error) {
      // Se não há sessão ativa, retorna null em vez de erro
      return null;
    }
  }

  // Realizar transação de caixa
  async performCashTransaction(data: CashTransactionRequest): Promise<CashTransaction> {
    const response = await makeRequest<CashTransaction>('/api/cash-register/transaction', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao realizar transação');
  }

  // Obter transações da sessão atual
  async getSessionTransactions(sessionId: string): Promise<CashTransaction[]> {
    const response = await makeRequest<CashTransaction[]>(`/api/cash-register/${sessionId}/transactions`);

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar transações');
  }

  // Calcular total das denominações
  calculateDenominationsTotal(denominations: CashDenominations): number {
    return (
      denominations.notes_10000 * 10000 +
      denominations.notes_5000 * 5000 +
      denominations.notes_2000 * 2000 +
      denominations.notes_1000 * 1000 +
      denominations.notes_500 * 500 +
      denominations.notes_200 * 200 +
      denominations.notes_100 * 100 +
      denominations.notes_50 * 50 +
      denominations.coins_10 * 10 +
      denominations.coins_5 * 5 +
      denominations.coins_1 * 1
    );
  }

  // Formatar valor monetário
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }
}

export const cashRegisterService = new CashRegisterService();
