import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Building2, CreditCard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AccountTypeSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AccountTypeSelectionModal: React.FC<AccountTypeSelectionModalProps> = ({
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();

  const handleSelectAccountType = (type: 'particular' | 'empresa') => {
    onClose();
    if (type === 'particular') {
      navigate('/clientes/abrir-conta-particular');
    } else {
      navigate('/clientes/abrir-conta-empresa');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-twins-primary" />
            Selecionar Tipo de Conta
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {/* Conta Particular */}
          <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-twins-primary/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg">Conta Particular</CardTitle>
              <CardDescription>
                Pessoas Singular (clientes individuais)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1 mb-4">
                <li>• Conta Particular/Singular</li>
                <li>• Conta Salário</li>
                <li>• Conta Júnior (2 titular)</li>
              </ul>
              <Button 
                onClick={() => handleSelectAccountType('particular')}
                className="w-full"
              >
                Abrir Conta Particular
              </Button>
            </CardContent>
          </Card>

          {/* Conta Empresa */}
          <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-twins-primary/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-lg">Conta Empresa</CardTitle>
              <CardDescription>
                Pessoas Colectivas (empresas)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1 mb-4">
                <li>• Conta Corrente Empresarial</li>
                <li>• Conta Poupança Empresarial</li>
              </ul>
              <Button 
                onClick={() => handleSelectAccountType('empresa')}
                className="w-full"
                variant="outline"
              >
                Abrir Conta Empresa
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Nota:</strong> Todas as contas passam pelo processo de aprovação. 
            Após criar a solicitação, ela será enviada para aprovação no menu "Contas → Aprovação de Contas".
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AccountTypeSelectionModal;
